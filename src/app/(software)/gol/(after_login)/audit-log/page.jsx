'use client';

import { useEffect, useState } from "react";
import { useSidebar } from "@/contexts/SidebarProvider";
import { DataTable } from "@/components/ui/Table";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Badge from "@/components/ui/Badge";
import { Select, SelectItem } from "@/components/ui/Select";
import {
  Search,
  Download,
  Filter,
  Calendar,
  User,
  Activity,
  Shield,
  TrendingUp,
  AlertTriangle,
  Clock,
  Globe,
  Eye,
  RefreshCw
} from "lucide-react";

// Mock audit log data - replace with actual data fetching
const mockAuditData = [
  {
    id: "1",
    timestamp: "2024-01-15 14:30:25",
    user: "<EMAIL>",
    action: "Order Created",
    resource: "Order #ORD-2024-001",
    details: "Created new CFS order for customer ABC Corp",
    ipAddress: "*************",
    status: "Success",
    module: "CFS Orders"
  },
  {
    id: "2",
    timestamp: "2024-01-15 14:25:10",
    user: "<EMAIL>",
    action: "User Login",
    resource: "Authentication",
    details: "Successful login attempt",
    ipAddress: "*************",
    status: "Success",
    module: "Authentication"
  },
  {
    id: "3",
    timestamp: "2024-01-15 14:20:45",
    user: "<EMAIL>",
    action: "Settings Updated",
    resource: "System Configuration",
    details: "Updated warehouse capacity settings",
    ipAddress: "*************",
    status: "Success",
    module: "System"
  },
  {
    id: "4",
    timestamp: "2024-01-15 14:15:30",
    user: "<EMAIL>",
    action: "Failed Login",
    resource: "Authentication",
    details: "Invalid password attempt",
    ipAddress: "*************",
    status: "Failed",
    module: "Authentication"
  },
  {
    id: "5",
    timestamp: "2024-01-15 14:10:15",
    user: "<EMAIL>",
    action: "Order Updated",
    resource: "Order #ORD-2024-002",
    details: "Updated shipping address for order",
    ipAddress: "*************",
    status: "Success",
    module: "Transport"
  }
];

// Define columns for the audit log table
const auditColumns = [
  {
    accessorKey: "timestamp",
    header: "Timestamp",
    filterable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-indigo-500" />
        <div className="font-mono text-sm bg-gray-50 px-2 py-1 rounded-md">
          {row.getValue("timestamp")}
        </div>
      </div>
    ),
  },
  {
    accessorKey: "user",
    header: "User",
    filterable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <User className="h-4 w-4 text-white" />
        </div>
        <div>
          <span className="text-sm font-medium text-gray-900">{row.getValue("user")}</span>
          <div className="text-xs text-gray-500">User Account</div>
        </div>
      </div>
    ),
  },
  {
    accessorKey: "action",
    header: "Action",
    filterable: true,
    cell: ({ row }) => {
      const action = row.getValue("action");
      const getActionColor = (action) => {
        if (action.includes("Login")) return "bg-green-100 text-green-800 border-green-200";
        if (action.includes("Created")) return "bg-blue-100 text-blue-800 border-blue-200";
        if (action.includes("Updated")) return "bg-yellow-100 text-yellow-800 border-yellow-200";
        if (action.includes("Failed")) return "bg-red-100 text-red-800 border-red-200";
        return "bg-gray-100 text-gray-800 border-gray-200";
      };
      return (
        <Badge className={`font-medium border ${getActionColor(action)}`}>
          {action}
        </Badge>
      );
    },
  },
  {
    accessorKey: "module",
    header: "Module",
    filterable: true,
    cell: ({ row }) => {
      const module = row.getValue("module");
      const getModuleIcon = (module) => {
        if (module === "Authentication") return <Shield className="h-3 w-3" />;
        if (module === "System") return <Activity className="h-3 w-3" />;
        return <Globe className="h-3 w-3" />;
      };
      return (
        <div className="flex items-center gap-2">
          {getModuleIcon(module)}
          <Badge variant="outline" className="text-xs font-medium">
            {module}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "resource",
    header: "Resource",
    filterable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Eye className="h-4 w-4 text-blue-500" />
        <span className="text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer">
          {row.getValue("resource")}
        </span>
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    filterable: true,
    cell: ({ row }) => {
      const status = row.getValue("status");
      const statusConfig = {
        "Success": {
          bg: "bg-emerald-100",
          text: "text-emerald-800",
          border: "border-emerald-200",
          icon: <TrendingUp className="h-3 w-3" />
        },
        "Failed": {
          bg: "bg-red-100",
          text: "text-red-800",
          border: "border-red-200",
          icon: <AlertTriangle className="h-3 w-3" />
        }
      };
      const config = statusConfig[status] || statusConfig["Success"];
      return (
        <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${config.bg} ${config.text} ${config.border}`}>
          {config.icon}
          {status}
        </div>
      );
    },
  },
  {
    accessorKey: "details",
    header: "Details",
    cell: ({ row }) => (
      <div className="max-w-xs">
        <div className="text-sm text-gray-700 truncate">
          {row.getValue("details")}
        </div>
        <div className="text-xs text-gray-500 mt-1">Click to view more</div>
      </div>
    ),
  },
  {
    accessorKey: "ipAddress",
    header: "IP Address",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Globe className="h-4 w-4 text-gray-400" />
        <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded text-gray-700">
          {row.getValue("ipAddress")}
        </span>
      </div>
    ),
  },
];

export default function AuditLogPage() {
  const { setTitle } = useSidebar();
  const [auditData, setAuditData] = useState(mockAuditData);
  const [dateRange, setDateRange] = useState("");
  const [selectedModule, setSelectedModule] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  useEffect(() => {
    setTitle("Audit Log");
  }, [setTitle]);

  const handleExport = () => {
    // Implementation for exporting audit logs
    console.log("Exporting audit logs...");
    // You can implement CSV/PDF export functionality here
  };

  const handleRefresh = () => {
    // Implementation for refreshing audit data
    console.log("Refreshing audit data...");
    // You can implement data refresh functionality here
  };

  const modules = ["All Modules", "Authentication", "CFS Orders", "Transport", "Warehouse", "System"];
  const statuses = ["All Statuses", "Success", "Failed", "Warning"];

  return (
    <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header Section */}
        <div className="relative overflow-hidden bg-white rounded-2xl shadow-xl border border-gray-200/50">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-indigo-600/5"></div>
          <div className="relative p-8">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                    Security Audit Log
                  </h1>
                  <p className="text-gray-600 mt-1">Monitor and track all system activities in real-time</p>
                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      Last updated: {new Date().toLocaleTimeString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Activity className="h-4 w-4" />
                      Live monitoring active
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  title="Refresh"
                  icon={<RefreshCw className="h-4 w-4" />}
                  onClick={handleRefresh}
                  className="hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
                />
                <Button
                  variant="default"
                  title="Export Report"
                  icon={<Download className="h-4 w-4" />}
                  onClick={handleExport}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>


        {/* Statistics Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-blue-600/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Total Events</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{auditData.length}</p>
                  <p className="text-sm text-blue-600 mt-1 flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    +12% from last week
                  </p>
                </div>
                <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Activity className="h-7 w-7 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-emerald-600/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Successful</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    {auditData.filter(item => item.status === "Success").length}
                  </p>
                  <p className="text-sm text-emerald-600 mt-1 flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    98.2% success rate
                  </p>
                </div>
                <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp className="h-7 w-7 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-red-600/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Failed</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    {auditData.filter(item => item.status === "Failed").length}
                  </p>
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertTriangle className="h-3 w-3" />
                    Requires attention
                  </p>
                </div>
                <div className="w-14 h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <AlertTriangle className="h-7 w-7 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-600/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Active Users</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    {new Set(auditData.map(item => item.user)).size}
                  </p>
                  <p className="text-sm text-purple-600 mt-1 flex items-center gap-1">
                    <User className="h-3 w-3" />
                    Online now
                  </p>
                </div>
                <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <User className="h-7 w-7 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Audit Events Table */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200/50">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold flex items-center gap-3 text-gray-800">
                  <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Eye className="h-4 w-4 text-white" />
                  </div>
                  Recent Audit Events
                </h2>
                <p className="text-sm text-gray-600 mt-1">Real-time monitoring of all system activities</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">Live</span>
              </div>
            </div>
          </div>
          <div className="p-6">
            <DataTable
              data={auditData}
              columns={auditColumns}
              displayButtons={true}
              displayFilters={true}
            />
          </div>
        </div>
      </div>
    </section>
  );
}
