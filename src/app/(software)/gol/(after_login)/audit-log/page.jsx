'use client';

import { useEffect, useState, useMemo } from "react";
import { useSidebar } from "@/contexts/SidebarProvider";
import { useAuth } from "@/contexts/AuthContext";
import { useCollection } from "@/hooks/useCollection";
import { DataTable } from "@/components/ui/Table";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Badge from "@/components/ui/Badge";
import { Select, SelectItem } from "@/components/ui/Select";
import {
  Search,
  Download,
  Filter,
  Calendar,
  User,
  Activity,
  Shield,
  TrendingUp,
  AlertTriangle,
  Clock,
  Globe,
  Eye,
  RefreshCw
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (error) {
    return dateString;
  }
};

// Helper function to get user display name
const getUserDisplayName = (userRecord) => {
  if (!userRecord) return 'System';

  if (userRecord.firstname && userRecord.lastname) {
    return `${userRecord.firstname} ${userRecord.lastname}`;
  }

  if (userRecord.username) {
    return userRecord.username;
  }

  if (userRecord.email) {
    return userRecord.email;
  }

  return 'Unknown User';
};

// Helper function to determine status based on action
const getActionStatus = (action) => {
  if (!action) return 'Success';

  const failureActions = ['Failed', 'Error', 'Denied', 'Rejected'];
  const isFailure = failureActions.some(failure =>
    action.toLowerCase().includes(failure.toLowerCase())
  );

  return isFailure ? 'Failed' : 'Success';
};

// Define columns for the audit log table
const auditColumns = [
  {
    accessorKey: "created",
    header: "Timestamp",
    filterable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-indigo-500" />
        <div className="font-mono text-sm bg-gray-50 px-2 py-1 rounded-md">
          {formatDate(row.getValue("created"))}
        </div>
      </div>
    ),
  },
  {
    accessorKey: "expand.user",
    header: "User",
    filterable: true,
    cell: ({ row }) => {
      const userRecord = row.original.expand?.user;
      const displayName = getUserDisplayName(userRecord);
      const userEmail = userRecord?.email || 'No email';

      return (
        <div className="flex items-center gap-3">
          <Avatar>
            <AvatarFallback>{displayName.charAt(0).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div>
            <span className="text-sm font-medium text-gray-900">{displayName}</span>
            <div className="text-xs text-gray-500">{userEmail}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    filterable: true,
    cell: ({ row }) => {
      const action = row.getValue("action");
      return (
        <Badge variant="outline">
          {action}
        </Badge>
      );
    },
  },
  {
    accessorKey: "module",
    header: "Module",
    filterable: true,
    cell: ({ row }) => {
      const module = row.getValue("module") || 'System';
      const subModule = row.original.subModule;
      const getModuleIcon = (module) => {
        if (module.toLowerCase().includes("auth")) return <Shield className="h-3 w-3" />;
        if (module.toLowerCase().includes("system")) return <Activity className="h-3 w-3" />;
        if (module.toLowerCase().includes("order")) return <Globe className="h-3 w-3" />;
        return <Globe className="h-3 w-3" />;
      };
      return (
        <div className="flex items-center gap-2">
          {getModuleIcon(module)}
          <div className="flex flex-col">
            <Badge variant="outline" className="text-xs font-medium">
              {module}
            </Badge>
            {subModule && (
              <span className="text-xs text-gray-500 mt-1">{subModule}</span>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "action",
    header: "Status",
    filterable: true,
    cell: ({ row }) => {
      const action = row.getValue("action");
      const status = getActionStatus(action);
      const statusConfig = {
        "Success": {
          bg: "bg-emerald-100",
          text: "text-emerald-800",
          border: "border-emerald-200",
          icon: <TrendingUp className="h-3 w-3" />
        },
        "Failed": {
          bg: "bg-red-100",
          text: "text-red-800",
          border: "border-red-200",
          icon: <AlertTriangle className="h-3 w-3" />
        }
      };
      const config = statusConfig[status] || statusConfig["Success"];
      return (
        <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${config.bg} ${config.text} ${config.border}`}>
          {config.icon}
          {status}
        </div>
      );
    },
  },
  {
    accessorKey: "details",
    header: "Details",
    cell: ({ row }) => {
      const details = row.getValue("details") || 'No details available';
      return (
        <div className="max-w-xs">
          <div className="text-sm text-gray-700 truncate" title={details}>
            {details}
          </div>
          <div className="text-xs text-gray-500 mt-1">Hover to view full text</div>
        </div>
      );
    },
  },
];

export default function AuditLogPage() {
  const { setTitle } = useSidebar();
  const { user, loading: authLoading } = useAuth();
  const [dateRange, setDateRange] = useState("");
  const [selectedModule, setSelectedModule] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedUser, setSelectedUser] = useState("");

  // Fetch audit logs from PocketBase
  const {
    data: auditData,
    error: auditError,
    mutation: refreshAuditData,
    fetchData: refetchAuditData
  } = useCollection('audit_logs', {
    expand: 'user',
    sort: '-created',
    // Add pagination if needed
    perPage: 100
  });

  // Fetch all users for filter dropdown
  const {
    data: usersData,
    error: usersError
  } = useCollection('users', {
    sort: 'firstname,lastname,username',
    fields: 'id,firstname,lastname,username,email,role'
  });

  useEffect(() => {
    setTitle("Audit Log");
  }, [setTitle]);

  // Filter data based on selected filters
  const filteredAuditData = useMemo(() => {
    if (!auditData) return [];

    return auditData.filter(item => {
      // Date filter
      if (dateRange) {
        const itemDate = new Date(item.created).toDateString();
        const filterDate = new Date(dateRange).toDateString();
        if (itemDate !== filterDate) return false;
      }

      // Module filter
      if (selectedModule && selectedModule !== "All Modules") {
        if (!item.module || item.module !== selectedModule) return false;
      }

      // Status filter
      if (selectedStatus && selectedStatus !== "All Statuses") {
        const itemStatus = getActionStatus(item.action);
        if (itemStatus !== selectedStatus) return false;
      }

      // User filter
      if (selectedUser && selectedUser !== "All Users") {
        if (!item.user || item.user !== selectedUser) return false;
      }

      return true;
    });
  }, [auditData, dateRange, selectedModule, selectedStatus, selectedUser]);

  const handleExport = () => {
    if (!filteredAuditData || filteredAuditData.length === 0) {
      alert("No data to export");
      return;
    }

    // Create CSV content
    const headers = ['Timestamp', 'User', 'Action', 'Module', 'Sub Module', 'Details'];
    const csvContent = [
      headers.join(','),
      ...filteredAuditData.map(item => [
        formatDate(item.created),
        getUserDisplayName(item.expand?.user),
        item.action || 'N/A',
        item.module || 'N/A',
        item.subModule || 'N/A',
        `"${(item.details || 'N/A').replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `audit_log_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleRefresh = () => {
    refetchAuditData();
  };

  // Get unique modules from data for filter dropdown
  const modules = useMemo(() => {
    if (!auditData) return ["All Modules"];
    const uniqueModules = [...new Set(auditData.map(item => item.module).filter(Boolean))];
    return ["All Modules", ...uniqueModules];
  }, [auditData]);

  // Get users for filter dropdown
  const users = useMemo(() => {
    if (!usersData) return ["All Users"];
    const userOptions = usersData.map(user => ({
      id: user.id,
      label: getUserDisplayName(user),
      role: user.role
    }));
    return ["All Users", ...userOptions];
  }, [usersData]);

  const statuses = ["All Statuses", "Success", "Failed"];

  // Calculate statistics
  const statistics = useMemo(() => {
    if (!filteredAuditData) return { total: 0, success: 0, failed: 0, uniqueUsers: 0 };

    const total = filteredAuditData.length;
    const success = filteredAuditData.filter(item => getActionStatus(item.action) === 'Success').length;
    const failed = filteredAuditData.filter(item => getActionStatus(item.action) === 'Failed').length;
    const uniqueUsers = new Set(filteredAuditData.map(item => item.user).filter(Boolean)).size;

    return { total, success, failed, uniqueUsers };
  }, [filteredAuditData]);

  // Authentication check
  if (authLoading) {
    return (
      <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-lg">Authenticating...</span>
          </div>
        </div>
      </section>
    );
  }

  // User access check (only allow certain roles to view audit logs)
  if (!user) {
    return (
      <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="p-6 rounded-xl shadow-2xl bg-red-50 border border-red-200">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Access Denied</h2>
            <p className="text-red-600">You must be logged in to view audit logs.</p>
          </div>
        </div>
      </section>
    );
  }

  // Role-based access control (optional - uncomment if needed)
  // const allowedRoles = ['Admin', 'Super Admin', 'Manager'];
  // if (!allowedRoles.includes(user.role)) {
  //   return (
  //     <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
  //       <div className="max-w-7xl mx-auto">
  //         <div className="p-6 rounded-xl shadow-2xl bg-yellow-50 border border-yellow-200">
  //           <h2 className="text-lg font-semibold text-yellow-800 mb-2">Insufficient Permissions</h2>
  //           <p className="text-yellow-600">Your role ({user.role}) does not have permission to view audit logs.</p>
  //         </div>
  //       </div>
  //     </section>
  //   );
  // }

  // Loading state
  if (!auditData && !auditError) {
    return (
      <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-lg">Loading audit logs...</span>
          </div>
        </div>
      </section>
    );
  }

  // Error state
  if (auditError) {
    return (
      <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="p-6 rounded-xl shadow-2xl bg-red-50 border border-red-200">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Audit Logs</h2>
            <p className="text-red-600 mb-4">
              {auditError.message || 'Failed to load audit data. Please try refreshing the page.'}
            </p>
            <Button
              variant="outline"
              title="Retry"
              icon={<RefreshCw className="h-4 w-4" />}
              onClick={handleRefresh}
              className="border-red-300 text-red-700 hover:bg-red-100"
            />
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header Section */}
        <div className="relative overflow-hidden bg-white rounded-2xl shadow-xl border border-gray-200/50">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-indigo-600/5"></div>
          <div className="relative p-8">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                    Security Audit Log
                  </h1>
                  <p className="text-gray-600 mt-1">Monitor and track all system activities in real-time</p>
                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      Last updated: {new Date().toLocaleTimeString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Activity className="h-4 w-4" />
                      Live monitoring active
                    </span>
                    <span className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      Logged in as: {getUserDisplayName(user)}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  title="Refresh"
                  icon={<RefreshCw className="h-4 w-4" />}
                  onClick={handleRefresh}
                  className="hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
                />
                <Button
                  variant="default"
                  title="Export Report"
                  icon={<Download className="h-4 w-4" />}
                  onClick={handleExport}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Advanced Filters Section */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200/50">
            <h2 className="text-lg font-semibold flex items-center gap-3 text-gray-800">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <Filter className="h-4 w-4 text-white" />
              </div>
              Advanced Filters
            </h2>
            <p className="text-sm text-gray-600 mt-1">Refine your audit log search with precise filters</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  Date Range
                </label>
                <Input
                  type="date"
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg transition-all duration-200"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <Activity className="h-4 w-4 text-purple-500" />
                  Module
                </label>
                <Select value={selectedModule} onValueChange={setSelectedModule}>
                  {modules.map((module) => (
                    <SelectItem key={module} value={module}>
                      {module}
                    </SelectItem>
                  ))}
                </Select>
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  Status
                </label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  {statuses.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </Select>
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <User className="h-4 w-4 text-indigo-500" />
                  User
                </label>
                <Select value={selectedUser} onValueChange={setSelectedUser}>
                  {users.map((user, index) => (
                    <SelectItem
                      key={typeof user === 'string' ? user : user.id}
                      value={typeof user === 'string' ? user : user.id}
                    >
                      {typeof user === 'string' ? user : `${user.label} (${user.role})`}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-blue-600/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Total Events</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{statistics.total}</p>
                  <p className="text-sm text-blue-600 mt-1 flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {filteredAuditData?.length !== auditData?.length ? 'Filtered results' : 'All records'}
                  </p>
                </div>
                <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Activity className="h-7 w-7 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-emerald-600/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Successful</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{statistics.success}</p>
                  <p className="text-sm text-emerald-600 mt-1 flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {statistics.total > 0 ? `${((statistics.success / statistics.total) * 100).toFixed(1)}% success rate` : 'No data'}
                  </p>
                </div>
                <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp className="h-7 w-7 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-red-600/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Failed</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{statistics.failed}</p>
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertTriangle className="h-3 w-3" />
                    {statistics.failed > 0 ? 'Requires attention' : 'All good'}
                  </p>
                </div>
                <div className="w-14 h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <AlertTriangle className="h-7 w-7 text-white" />
                </div>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-600/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Active Users</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{statistics.uniqueUsers}</p>
                  <p className="text-sm text-purple-600 mt-1 flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {statistics.uniqueUsers > 0 ? 'Users tracked' : 'No activity'}
                  </p>
                </div>
                <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <User className="h-7 w-7 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Audit Events Table */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200/50">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold flex items-center gap-3 text-gray-800">
                  <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Eye className="h-4 w-4 text-white" />
                  </div>
                  Recent Audit Events
                </h2>
                <p className="text-sm text-gray-600 mt-1">Real-time monitoring of all system activities</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">Live</span>
              </div>
            </div>
          </div>
          <div className="p-6">
            <DataTable
              data={filteredAuditData || []}
              columns={auditColumns}
              displayButtons={true}
              displayFilters={true}
            />
          </div>
        </div>
      </div>
    </section>
  );
}
