'use client';

import { useEffect, useState } from "react";
import { useSidebar } from "@/contexts/SidebarProvider";
import { DataTable } from "@/components/ui/Table";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Badge from "@/components/ui/Badge";
import { Select, SelectItem } from "@/components/ui/Select";
import { Search, Download, Filter, Calendar, User, Activity } from "lucide-react";

// Mock audit log data - replace with actual data fetching
const mockAuditData = [
  {
    id: "1",
    timestamp: "2024-01-15 14:30:25",
    user: "<EMAIL>",
    action: "Order Created",
    resource: "Order #ORD-2024-001",
    details: "Created new CFS order for customer ABC Corp",
    ipAddress: "*************",
    status: "Success",
    module: "CFS Orders"
  },
  {
    id: "2",
    timestamp: "2024-01-15 14:25:10",
    user: "<EMAIL>",
    action: "User Login",
    resource: "Authentication",
    details: "Successful login attempt",
    ipAddress: "*************",
    status: "Success",
    module: "Authentication"
  },
  {
    id: "3",
    timestamp: "2024-01-15 14:20:45",
    user: "<EMAIL>",
    action: "Settings Updated",
    resource: "System Configuration",
    details: "Updated warehouse capacity settings",
    ipAddress: "*************",
    status: "Success",
    module: "System"
  },
  {
    id: "4",
    timestamp: "2024-01-15 14:15:30",
    user: "<EMAIL>",
    action: "Failed Login",
    resource: "Authentication",
    details: "Invalid password attempt",
    ipAddress: "*************",
    status: "Failed",
    module: "Authentication"
  },
  {
    id: "5",
    timestamp: "2024-01-15 14:10:15",
    user: "<EMAIL>",
    action: "Order Updated",
    resource: "Order #ORD-2024-002",
    details: "Updated shipping address for order",
    ipAddress: "*************",
    status: "Success",
    module: "Transport"
  }
];

// Define columns for the audit log table
const auditColumns = [
  {
    accessorKey: "timestamp",
    header: "Timestamp",
    filterable: true,
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        {row.getValue("timestamp")}
      </div>
    ),
  },
  {
    accessorKey: "user",
    header: "User",
    filterable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <User className="h-4 w-4 text-gray-500" />
        <span className="text-sm">{row.getValue("user")}</span>
      </div>
    ),
  },
  {
    accessorKey: "action",
    header: "Action",
    filterable: true,
    cell: ({ row }) => (
      <Badge variant="secondary" className="font-medium">
        {row.getValue("action")}
      </Badge>
    ),
  },
  {
    accessorKey: "module",
    header: "Module",
    filterable: true,
    cell: ({ row }) => (
      <Badge variant="outline" className="text-xs">
        {row.getValue("module")}
      </Badge>
    ),
  },
  {
    accessorKey: "resource",
    header: "Resource",
    filterable: true,
    cell: ({ row }) => (
      <span className="text-sm font-medium text-blue-600">
        {row.getValue("resource")}
      </span>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    filterable: true,
    cell: ({ row }) => {
      const status = row.getValue("status");
      return (
        <Badge 
          variant={status === "Success" ? "default" : "destructive"}
          className="text-xs"
        >
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "details",
    header: "Details",
    cell: ({ row }) => (
      <div className="max-w-xs truncate text-sm text-gray-600">
        {row.getValue("details")}
      </div>
    ),
  },
  {
    accessorKey: "ipAddress",
    header: "IP Address",
    cell: ({ row }) => (
      <span className="font-mono text-xs text-gray-500">
        {row.getValue("ipAddress")}
      </span>
    ),
  },
];

export default function AuditLogPage() {
  const { setTitle } = useSidebar();
  const [auditData, setAuditData] = useState(mockAuditData);
  const [dateRange, setDateRange] = useState("");
  const [selectedModule, setSelectedModule] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  useEffect(() => {
    setTitle("Audit Log");
  }, [setTitle]);

  const handleExport = () => {
    // Implementation for exporting audit logs
    console.log("Exporting audit logs...");
    // You can implement CSV/PDF export functionality here
  };

  const handleRefresh = () => {
    // Implementation for refreshing audit data
    console.log("Refreshing audit data...");
    // You can implement data refresh functionality here
  };

  const modules = ["All Modules", "Authentication", "CFS Orders", "Transport", "Warehouse", "System"];
  const statuses = ["All Statuses", "Success", "Failed", "Warning"];

  return (
    <section className="grid gap-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <Activity className="h-6 w-6 text-primary" />
          <div>
            <h1 className="text-2xl font-bold">Audit Log</h1>
            <p className="text-sm text-gray-600">Track all system activities and user actions</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            title="Refresh"
            icon={<Activity className="h-4 w-4" />}
            onClick={handleRefresh}
          />
          <Button
            variant="default"
            title="Export"
            icon={<Download className="h-4 w-4" />}
            onClick={handleExport}
          />
        </div>
      </div>

      {/* Filters Section */}
      <div className="p-6 rounded-xl shadow-2xl bg-[var(--accent)]">
        <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Filters
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Date Range</label>
            <Input
              type="date"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-full"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Module</label>
            <Select value={selectedModule} onValueChange={setSelectedModule}>
              {modules.map((module) => (
                <SelectItem key={module} value={module}>
                  {module}
                </SelectItem>
              ))}
            </Select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Status</label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              {statuses.map((status) => (
                <SelectItem key={status} value={status}>
                  {status}
                </SelectItem>
              ))}
            </Select>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">Total Events</p>
              <p className="text-2xl font-bold text-blue-800">{auditData.length}</p>
            </div>
            <Activity className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="p-4 rounded-lg bg-green-50 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">Successful</p>
              <p className="text-2xl font-bold text-green-800">
                {auditData.filter(item => item.status === "Success").length}
              </p>
            </div>
            <Activity className="h-8 w-8 text-green-500" />
          </div>
        </div>
        <div className="p-4 rounded-lg bg-red-50 border border-red-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-600">Failed</p>
              <p className="text-2xl font-bold text-red-800">
                {auditData.filter(item => item.status === "Failed").length}
              </p>
            </div>
            <Activity className="h-8 w-8 text-red-500" />
          </div>
        </div>
        <div className="p-4 rounded-lg bg-purple-50 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">Unique Users</p>
              <p className="text-2xl font-bold text-purple-800">
                {new Set(auditData.map(item => item.user)).size}
              </p>
            </div>
            <User className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Audit Log Table */}
      <div className="p-6 rounded-xl shadow-2xl bg-[var(--accent)]">
        <h2 className="text-xl font-semibold mb-4">Audit Events</h2>
        <DataTable
          data={auditData}
          columns={auditColumns}
          displayButtons={true}
          displayFilters={true}
        />
      </div>
    </section>
  );
}
