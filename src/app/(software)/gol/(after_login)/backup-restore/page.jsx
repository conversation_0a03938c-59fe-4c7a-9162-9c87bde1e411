'use client';

import { useEffect, useState } from "react";
import { useSidebar } from "@/contexts/SidebarProvider";
import { DataTable } from "@/components/ui/Table";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Badge from "@/components/ui/Badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/Tabs";
import { 
  Database, 
  Download, 
  Upload, 
  RefreshCw, 
  Calendar, 
  HardDrive,
  CheckCircle,
  AlertCircle,
  Clock,
  FileText,
  Settings
} from "lucide-react";

// Mock backup data - replace with actual data fetching
const mockBackupData = [
  {
    id: "1",
    name: "daily_backup_2024_01_15",
    type: "Automatic",
    size: "2.4 GB",
    created: "2024-01-15 02:00:00",
    status: "Completed",
    description: "Daily automated backup",
    retention: "30 days"
  },
  {
    id: "2",
    name: "manual_backup_2024_01_14",
    type: "Manual",
    size: "2.3 GB",
    created: "2024-01-14 16:30:00",
    status: "Completed",
    description: "Pre-update manual backup",
    retention: "90 days"
  },
  {
    id: "3",
    name: "weekly_backup_2024_01_08",
    type: "Automatic",
    size: "2.1 GB",
    created: "2024-01-08 02:00:00",
    status: "Completed",
    description: "Weekly automated backup",
    retention: "90 days"
  },
  {
    id: "4",
    name: "backup_in_progress",
    type: "Manual",
    size: "1.8 GB",
    created: "2024-01-15 14:45:00",
    status: "In Progress",
    description: "Current backup operation",
    retention: "30 days"
  }
];

// Mock restore history data
const mockRestoreHistory = [
  {
    id: "1",
    backupName: "daily_backup_2024_01_10",
    restoredAt: "2024-01-12 10:30:00",
    restoredBy: "<EMAIL>",
    status: "Success",
    duration: "45 minutes",
    reason: "System recovery after hardware failure"
  },
  {
    id: "2",
    backupName: "manual_backup_2024_01_05",
    restoredAt: "2024-01-06 14:15:00",
    restoredBy: "<EMAIL>",
    status: "Success",
    duration: "38 minutes",
    reason: "Data corruption recovery"
  }
];

// Define columns for backup table
const backupColumns = [
  {
    accessorKey: "name",
    header: "Backup Name",
    filterable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Database className="h-4 w-4 text-blue-500" />
        <span className="font-medium">{row.getValue("name")}</span>
      </div>
    ),
  },
  {
    accessorKey: "type",
    header: "Type",
    filterable: true,
    cell: ({ row }) => (
      <Badge variant={row.getValue("type") === "Automatic" ? "default" : "secondary"}>
        {row.getValue("type")}
      </Badge>
    ),
  },
  {
    accessorKey: "size",
    header: "Size",
    cell: ({ row }) => (
      <span className="font-mono text-sm">{row.getValue("size")}</span>
    ),
  },
  {
    accessorKey: "created",
    header: "Created",
    filterable: true,
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        {row.getValue("created")}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    filterable: true,
    cell: ({ row }) => {
      const status = row.getValue("status");
      const variant = status === "Completed" ? "default" : 
                    status === "In Progress" ? "secondary" : "destructive";
      return (
        <Badge variant={variant} className="text-xs">
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "retention",
    header: "Retention",
    cell: ({ row }) => (
      <span className="text-sm text-gray-600">{row.getValue("retention")}</span>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const backup = row.original;
      return (
        <div className="flex gap-2">
          <Button
            variant="outline"
            title="Download"
            icon={<Download className="h-4 w-4" />}
            textSize="text-xs"
            className="px-2 py-1"
            disabled={backup.status !== "Completed"}
          />
          <Button
            variant="outline"
            title="Restore"
            icon={<RefreshCw className="h-4 w-4" />}
            textSize="text-xs"
            className="px-2 py-1"
            disabled={backup.status !== "Completed"}
          />
        </div>
      );
    },
  },
];

// Define columns for restore history table
const restoreColumns = [
  {
    accessorKey: "backupName",
    header: "Backup Name",
    filterable: true,
    cell: ({ row }) => (
      <span className="font-medium">{row.getValue("backupName")}</span>
    ),
  },
  {
    accessorKey: "restoredAt",
    header: "Restored At",
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        {row.getValue("restoredAt")}
      </div>
    ),
  },
  {
    accessorKey: "restoredBy",
    header: "Restored By",
    cell: ({ row }) => (
      <span className="text-sm">{row.getValue("restoredBy")}</span>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status");
      return (
        <Badge variant={status === "Success" ? "default" : "destructive"}>
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "duration",
    header: "Duration",
    cell: ({ row }) => (
      <span className="text-sm">{row.getValue("duration")}</span>
    ),
  },
  {
    accessorKey: "reason",
    header: "Reason",
    cell: ({ row }) => (
      <div className="max-w-xs truncate text-sm text-gray-600">
        {row.getValue("reason")}
      </div>
    ),
  },
];

export default function BackupRestorePage() {
  const { setTitle } = useSidebar();
  const [backupData, setBackupData] = useState(mockBackupData);
  const [restoreHistory, setRestoreHistory] = useState(mockRestoreHistory);
  const [activeTab, setActiveTab] = useState("backups");

  useEffect(() => {
    setTitle("Backup & Restore");
  }, [setTitle]);

  const handleCreateBackup = () => {
    console.log("Creating new backup...");
    // Implementation for creating a new backup
  };

  const handleUploadBackup = () => {
    console.log("Uploading backup file...");
    // Implementation for uploading backup file
  };

  const handleRefresh = () => {
    console.log("Refreshing backup data...");
    // Implementation for refreshing data
  };

  return (
    <section className="grid gap-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <Database className="h-6 w-6 text-primary" />
          <div>
            <h1 className="text-2xl font-bold">Backup & Restore</h1>
            <p className="text-sm text-gray-600">Manage system backups and restore operations</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            title="Refresh"
            icon={<RefreshCw className="h-4 w-4" />}
            onClick={handleRefresh}
          />
          <Button
            variant="outline"
            title="Upload Backup"
            icon={<Upload className="h-4 w-4" />}
            onClick={handleUploadBackup}
          />
          <Button
            variant="default"
            title="Create Backup"
            icon={<Database className="h-4 w-4" />}
            onClick={handleCreateBackup}
          />
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">Total Backups</p>
              <p className="text-2xl font-bold text-blue-800">{backupData.length}</p>
            </div>
            <Database className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="p-4 rounded-lg bg-green-50 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">Completed</p>
              <p className="text-2xl font-bold text-green-800">
                {backupData.filter(item => item.status === "Completed").length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>
        <div className="p-4 rounded-lg bg-yellow-50 border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600">In Progress</p>
              <p className="text-2xl font-bold text-yellow-800">
                {backupData.filter(item => item.status === "In Progress").length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-500" />
          </div>
        </div>
        <div className="p-4 rounded-lg bg-purple-50 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">Total Size</p>
              <p className="text-2xl font-bold text-purple-800">9.6 GB</p>
            </div>
            <HardDrive className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Main Content with Tabs */}
      <div className="p-6 rounded-xl shadow-2xl bg-[var(--accent)]">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="backups">Backups</TabsTrigger>
            <TabsTrigger value="restore-history">Restore History</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="backups" className="mt-6">
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Available Backups</h2>
              <DataTable
                data={backupData}
                columns={backupColumns}
                displayButtons={true}
                displayFilters={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="restore-history" className="mt-6">
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Restore History</h2>
              <DataTable
                data={restoreHistory}
                columns={restoreColumns}
                displayButtons={true}
                displayFilters={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <div className="space-y-6">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Backup Settings
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Automatic Backup Schedule</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">Daily Backup</p>
                        <p className="text-sm text-gray-600">Every day at 2:00 AM</p>
                      </div>
                      <Badge variant="default">Enabled</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">Weekly Backup</p>
                        <p className="text-sm text-gray-600">Every Sunday at 1:00 AM</p>
                      </div>
                      <Badge variant="default">Enabled</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">Monthly Backup</p>
                        <p className="text-sm text-gray-600">First day of month at 12:00 AM</p>
                      </div>
                      <Badge variant="secondary">Disabled</Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Retention Policies</h3>
                  <div className="space-y-3">
                    <div className="p-3 border rounded-lg">
                      <p className="font-medium">Daily Backups</p>
                      <p className="text-sm text-gray-600">Retain for 30 days</p>
                    </div>
                    <div className="p-3 border rounded-lg">
                      <p className="font-medium">Weekly Backups</p>
                      <p className="text-sm text-gray-600">Retain for 90 days</p>
                    </div>
                    <div className="p-3 border rounded-lg">
                      <p className="font-medium">Monthly Backups</p>
                      <p className="text-sm text-gray-600">Retain for 1 year</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <Button
                  variant="default"
                  title="Save Settings"
                  icon={<Settings className="h-4 w-4" />}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
