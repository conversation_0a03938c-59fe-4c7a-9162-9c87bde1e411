import pbclient from '@/lib/db';

/**
 * Utility function to create audit log entries
 * @param {Object} params - Audit log parameters
 * @param {string} params.action - Action performed (Create, Edit, Delete)
 * @param {string} params.module - Module name (e.g., "Orders", "Users", "System")
 * @param {string} params.subModule - Sub-module name (optional)
 * @param {string} params.details - Detailed description of the action
 * @param {string} params.userId - ID of the user performing the action (optional, will use current user if not provided)
 * @returns {Promise<Object|null>} Created audit log record or null if failed
 */
export async function createAuditLog({
  action,
  module,
  subModule = null,
  details,
  userId = null
}) {
  try {
    // Get current user if userId not provided
    const currentUser = userId || pbclient.authStore.record?.id;
    
    if (!currentUser) {
      console.warn('No user found for audit log entry');
      return null;
    }

    // Validate required fields
    if (!action || !module || !details) {
      console.error('Missing required fields for audit log:', { action, module, details });
      return null;
    }

    // Create audit log entry
    const auditData = {
      user: currentUser,
      action: action,
      module: module,
      subModule: subModule,
      details: details
    };

    const record = await pbclient.collection('audit_logs').create(auditData);
    
    console.log('Audit log created:', record);
    return record;
    
  } catch (error) {
    console.error('Failed to create audit log:', error);
    return null;
  }
}

/**
 * Predefined audit log actions
 */
export const AUDIT_ACTIONS = {
  CREATE: 'Create',
  EDIT: 'Edit',
  DELETE: 'Delete'
};

/**
 * Predefined module names
 */
export const AUDIT_MODULES = {
  AUTHENTICATION: 'Authentication',
  USERS: 'Users',
  ORDERS: 'Orders',
  CFS_ORDERS: 'CFS Orders',
  TRANSPORT_ORDERS: 'Transport Orders',
  WAREHOUSE_ORDERS: 'Warehouse Orders',
  PRICING: 'Pricing',
  TARIFF: 'Tariff',
  SYSTEM: 'System',
  SETTINGS: 'Settings',
  BACKUP: 'Backup',
  REPORTS: 'Reports'
};

/**
 * Helper functions for common audit log scenarios
 */

// User authentication logs
export const logUserLogin = (userEmail) => {
  return createAuditLog({
    action: 'Login',
    module: AUDIT_MODULES.AUTHENTICATION,
    details: `User ${userEmail} logged in successfully`
  });
};

export const logUserLogout = (userEmail) => {
  return createAuditLog({
    action: 'Logout',
    module: AUDIT_MODULES.AUTHENTICATION,
    details: `User ${userEmail} logged out`
  });
};

export const logFailedLogin = (userEmail) => {
  return createAuditLog({
    action: 'Failed Login',
    module: AUDIT_MODULES.AUTHENTICATION,
    details: `Failed login attempt for ${userEmail}`
  });
};

// Order management logs
export const logOrderCreated = (orderType, orderId, customerName) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.CREATE,
    module: AUDIT_MODULES.ORDERS,
    subModule: orderType,
    details: `Created new ${orderType} order ${orderId} for customer ${customerName}`
  });
};

export const logOrderUpdated = (orderType, orderId, changes) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.EDIT,
    module: AUDIT_MODULES.ORDERS,
    subModule: orderType,
    details: `Updated ${orderType} order ${orderId}: ${changes}`
  });
};

export const logOrderDeleted = (orderType, orderId) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.DELETE,
    module: AUDIT_MODULES.ORDERS,
    subModule: orderType,
    details: `Deleted ${orderType} order ${orderId}`
  });
};

// User management logs
export const logUserCreated = (newUserEmail, role) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.CREATE,
    module: AUDIT_MODULES.USERS,
    details: `Created new user account for ${newUserEmail} with role ${role}`
  });
};

export const logUserUpdated = (targetUserEmail, changes) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.EDIT,
    module: AUDIT_MODULES.USERS,
    details: `Updated user ${targetUserEmail}: ${changes}`
  });
};

export const logUserDeleted = (targetUserEmail) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.DELETE,
    module: AUDIT_MODULES.USERS,
    details: `Deleted user account for ${targetUserEmail}`
  });
};

// System logs
export const logSystemSettingsChanged = (settingName, oldValue, newValue) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.EDIT,
    module: AUDIT_MODULES.SYSTEM,
    subModule: AUDIT_MODULES.SETTINGS,
    details: `Changed system setting '${settingName}' from '${oldValue}' to '${newValue}'`
  });
};

export const logBackupCreated = (backupName, size) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.CREATE,
    module: AUDIT_MODULES.BACKUP,
    details: `Created system backup '${backupName}' (${size})`
  });
};

export const logBackupRestored = (backupName) => {
  return createAuditLog({
    action: 'Restore',
    module: AUDIT_MODULES.BACKUP,
    details: `Restored system from backup '${backupName}'`
  });
};

// Pricing and tariff logs
export const logPricingUpdated = (serviceType, changes) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.EDIT,
    module: AUDIT_MODULES.PRICING,
    subModule: serviceType,
    details: `Updated pricing for ${serviceType}: ${changes}`
  });
};

export const logTariffUploaded = (fileName, serviceType) => {
  return createAuditLog({
    action: AUDIT_ACTIONS.CREATE,
    module: AUDIT_MODULES.TARIFF,
    subModule: serviceType,
    details: `Uploaded new tariff file '${fileName}' for ${serviceType}`
  });
};

// Report generation logs
export const logReportGenerated = (reportType, parameters) => {
  return createAuditLog({
    action: 'Generate',
    module: AUDIT_MODULES.REPORTS,
    subModule: reportType,
    details: `Generated ${reportType} report with parameters: ${JSON.stringify(parameters)}`
  });
};

export default {
  createAuditLog,
  AUDIT_ACTIONS,
  AUDIT_MODULES,
  logUserLogin,
  logUserLogout,
  logFailedLogin,
  logOrderCreated,
  logOrderUpdated,
  logOrderDeleted,
  logUserCreated,
  logUserUpdated,
  logUserDeleted,
  logSystemSettingsChanged,
  logBackupCreated,
  logBackupRestored,
  logPricingUpdated,
  logTariffUploaded,
  logReportGenerated
};
