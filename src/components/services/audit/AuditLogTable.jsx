import { DataTable } from '@/components/ui/Table';
import Badge from '@/components/ui/Badge';
import { 
  Clock, 
  User, 
  Activity, 
  Shield, 
  Globe, 
  Eye, 
  TrendingUp, 
  AlertTriangle 
} from 'lucide-react';

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (error) {
    return dateString;
  }
};

// Helper function to get user display name
const getUserDisplayName = (userRecord) => {
  if (!userRecord) return 'System';
  
  if (userRecord.firstname && userRecord.lastname) {
    return `${userRecord.firstname} ${userRecord.lastname}`;
  }
  
  if (userRecord.username) {
    return userRecord.username;
  }
  
  if (userRecord.email) {
    return userRecord.email;
  }
  
  return 'Unknown User';
};

// Helper function to determine status based on action
const getActionStatus = (action) => {
  if (!action) return 'Success';
  
  const failureActions = ['Failed', 'Error', 'Denied', 'Rejected'];
  const isFailure = failureActions.some(failure => 
    action.toLowerCase().includes(failure.toLowerCase())
  );
  
  return isFailure ? 'Failed' : 'Success';
};

// Define columns for the audit log table
const auditColumns = [
  {
    accessorKey: "created",
    header: "Timestamp",
    filterable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-indigo-500" />
        <div className="font-mono text-sm bg-gray-50 px-2 py-1 rounded-md">
          {formatDate(row.getValue("created"))}
        </div>
      </div>
    ),
  },
  {
    accessorKey: "expand.user",
    header: "User",
    filterable: true,
    cell: ({ row }) => {
      const userRecord = row.original.expand?.user;
      const displayName = getUserDisplayName(userRecord);
      const userEmail = userRecord?.email || 'No email';
      
      return (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-white" />
          </div>
          <div>
            <span className="text-sm font-medium text-gray-900">{displayName}</span>
            <div className="text-xs text-gray-500">{userEmail}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    filterable: true,
    cell: ({ row }) => {
      const action = row.getValue("action");
      const getActionColor = (action) => {
        if (action && action.includes("Login")) return "bg-green-100 text-green-800 border-green-200";
        if (action && action.includes("Created")) return "bg-blue-100 text-blue-800 border-blue-200";
        if (action && action.includes("Updated")) return "bg-yellow-100 text-yellow-800 border-yellow-200";
        if (action && action.includes("Failed")) return "bg-red-100 text-red-800 border-red-200";
        return "bg-gray-100 text-gray-800 border-gray-200";
      };
      return (
        <Badge className={`font-medium border ${getActionColor(action)}`}>
          {action || 'Unknown'}
        </Badge>
      );
    },
  },
  {
    accessorKey: "module",
    header: "Module",
    filterable: true,
    cell: ({ row }) => {
      const module = row.getValue("module") || 'System';
      const subModule = row.original.subModule;
      const getModuleIcon = (module) => {
        if (module.toLowerCase().includes("auth")) return <Shield className="h-3 w-3" />;
        if (module.toLowerCase().includes("system")) return <Activity className="h-3 w-3" />;
        if (module.toLowerCase().includes("order")) return <Globe className="h-3 w-3" />;
        return <Globe className="h-3 w-3" />;
      };
      return (
        <div className="flex items-center gap-2">
          {getModuleIcon(module)}
          <div className="flex flex-col">
            <Badge variant="outline" className="text-xs font-medium">
              {module}
            </Badge>
            {subModule && (
              <span className="text-xs text-gray-500 mt-1">{subModule}</span>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "action",
    header: "Status",
    filterable: true,
    cell: ({ row }) => {
      const action = row.getValue("action");
      const status = getActionStatus(action);
      const statusConfig = {
        "Success": {
          bg: "bg-emerald-100",
          text: "text-emerald-800",
          border: "border-emerald-200",
          icon: <TrendingUp className="h-3 w-3" />
        },
        "Failed": {
          bg: "bg-red-100",
          text: "text-red-800",
          border: "border-red-200",
          icon: <AlertTriangle className="h-3 w-3" />
        }
      };
      const config = statusConfig[status] || statusConfig["Success"];
      return (
        <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${config.bg} ${config.text} ${config.border}`}>
          {config.icon}
          {status}
        </div>
      );
    },
  },
  {
    accessorKey: "details",
    header: "Details",
    cell: ({ row }) => {
      const details = row.getValue("details") || 'No details available';
      return (
        <div className="max-w-xs">
          <div className="text-sm text-gray-700 truncate" title={details}>
            {details}
          </div>
          <div className="text-xs text-gray-500 mt-1">Hover to view full text</div>
        </div>
      );
    },
  },
];

export default function AuditLogTable({ auditData, loading = false }) {
  return (
    <div className="bg-white rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
      <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200/50">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold flex items-center gap-3 text-gray-800">
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Eye className="h-4 w-4 text-white" />
              </div>
              Recent Audit Events
            </h2>
            <p className="text-sm text-gray-600 mt-1">Real-time monitoring of all system activities</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600">Live</span>
          </div>
        </div>
      </div>
      <div className="p-6">
        <DataTable
          data={auditData || []}
          columns={auditColumns}
          displayButtons={true}
          displayFilters={true}
          loading={loading}
        />
      </div>
    </div>
  );
}
